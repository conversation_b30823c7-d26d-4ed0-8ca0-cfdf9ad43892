{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load image_utils %}

{% block title %}تفاصيل الوظيفة - {{ job.jop_name }} - نظام الدولية{% endblock %}

{% block page_title %}تفاصيل الوظيفة: {{ job.jop_name }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:jobs:job_list' %}">الوظائف</a></li>
<li class="breadcrumb-item active">{{ job.jop_name }}</li>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-briefcase text-primary me-2"></i>{{ job.jop_name }}
                </h5>
                <div>
                    <a href="{% url 'Hr:jobs:edit' job.jop_code %}" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-1"></i> تعديل
                    </a>
                    <a href="{% url 'Hr:jobs:delete' job.jop_code %}" class="btn btn-outline-danger delete-btn" data-bs-toggle="modal" data-bs-target="#deleteModal" data-name="{{ job.jop_name }}" data-url="{% url 'Hr:jobs:delete' job.jop_code %}">
                        <i class="fas fa-trash-alt me-1"></i> حذف
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tbody>
                                <tr>
                                    <th class="ps-0" style="width: 40%;">رمز الوظيفة:</th>
                                    <td>{{ job.jop_code }}</td>
                                </tr>
                                <tr>
                                    <th class="ps-0">اسم الوظيفة:</th>
                                    <td>{{ job.jop_name }}</td>
                                </tr>
                                <tr>
                                    <th class="ps-0">القسم:</th>
                                    <td>{{ job.department.dept_name|default:"غير محدد" }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title mb-3">إحصائيات الوظيفة</h6>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="flex-shrink-0 me-3">
                                        <div class="avatar-sm bg-primary-subtle text-primary rounded-circle">
                                            <i class="fas fa-users"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0">عدد الموظفين</h6>
                                        <span class="text-muted">{{ employees.count }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Employees with this job -->
<div class="card shadow-sm">
    <div class="card-header bg-light py-3 d-flex justify-content-between align-items-center">
        <h5 class="mb-0 fw-semibold">
            <i class="fas fa-users text-primary me-2"></i>الموظفون في هذه الوظيفة
            <span class="badge bg-primary rounded-pill ms-2">{{ employees.count }}</span>
        </h5>
        <a href="{% url 'Hr:employees:create' %}" class="btn btn-sm btn-primary">
            <i class="fas fa-user-plus me-1"></i> إضافة موظف
        </a>
    </div>
    <div class="card-body p-0">
        {% if employees %}
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th class="py-3 px-4">رقم الموظف</th>
                        <th class="py-3 px-4">الاسم</th>
                        <th class="py-3 px-4">القسم</th>
                        <th class="py-3 px-4">تاريخ التعيين</th>
                        <th class="py-3 px-4">حالة العمل</th>
                        <th class="py-3 px-4 text-center">العمليات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees %}
                    <tr>
                        <td class="px-4">{{ employee.emp_id }}</td>
                        <td class="px-4">
                            <div class="d-flex align-items-center">
                                {% if employee.emp_image %}
                                <img src="{{ employee.emp_image|binary_to_img }}" alt="{{ employee.emp_full_name }}" class="rounded-circle me-2 object-fit-cover" width="40" height="40">
                                {% else %}
                                <div class="avatar bg-secondary text-white me-2 flex-shrink-0">
                                    {{ employee.emp_first_name|slice:":1"|upper }}
                                </div>
                                {% endif %}
                                <div>
                                    <a href="{% url 'Hr:employees:detail' employee.emp_id %}" class="fw-medium text-decoration-none text-dark">{{ employee.emp_full_name }}</a>
                                </div>
                            </div>
                        </td>
                        <td class="px-4">{{ employee.department.dept_name|default:"-" }}</td>
                        <td class="px-4">{{ employee.emp_date_hiring|date:"Y-m-d"|default:"-" }}</td>
                        <td class="px-4">
                            {% if employee.working_condition == 'سارى' %}
                            <span class="badge bg-success">نشط</span>
                            {% elif employee.working_condition == 'استقالة' %}
                            <span class="badge bg-warning">استقالة</span>
                            {% elif employee.working_condition == 'انقطاع عن العمل' %}
                            <span class="badge bg-danger">انقطاع</span>
                            {% else %}
                            <span class="badge bg-secondary">{{ employee.working_condition|default:"-" }}</span>
                            {% endif %}
                        </td>
                        <td class="px-4 text-center">
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'Hr:employees:detail' employee.emp_id %}" class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'Hr:employees:edit' employee.emp_id %}" class="btn btn-outline-secondary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x mb-3 text-muted"></i>
            <p class="mb-0">لا يوجد موظفين مسجلين في هذه الوظيفة حالياً.</p>
            <a href="{% url 'Hr:employees:create' %}" class="btn btn-primary mt-3">
                <i class="fas fa-user-plus me-1"></i> إضافة موظف جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الوظيفة: <strong id="itemName"></strong>؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تحذير: لا يمكن حذف الوظيفة إذا كان هناك موظفين مرتبطين بها.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="#" id="confirmDelete" class="btn btn-danger">تأكيد الحذف</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 1rem;
        font-weight: bold;
    }
    
    .avatar-sm {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 1rem;
    }
    
    .object-fit-cover {
        object-fit: cover;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle delete modal
        const deleteModal = document.getElementById('deleteModal');
        if (deleteModal) {
            deleteModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const itemName = button.getAttribute('data-name');
                const url = button.getAttribute('data-url');
                
                document.getElementById('itemName').textContent = itemName;
                document.getElementById('confirmDelete').setAttribute('href', url);
            });
        }
    });
</script>
{% endblock %}
